.container {
  background: #3646AC;
  position: absolute;
  color: #fff;
  padding: 0.625rem 0.5rem 0.625rem 0.75rem;
  font-weight: 500;
  z-index: 1;
  font-size: 0.75rem;
  white-space: nowrap;
  text-transform: uppercase;
}

.xSmall {
  padding: 0.15rem 0.5rem 0.15rem 0.75rem;
}

.small {
  padding: 0.25rem 0.3rem 0.25rem 0.3rem;
}

.large {
  border-radius: 0 0.4rem 0.4rem 0;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

.placement-top {
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.placement-bottom {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.placement-left {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.placement-right {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.placement-top-left {
  border-top-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.placement-top-right {
  border-top-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.placement-bottom-left {
  border-top-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.placement-bottom-right {
  border-top-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
