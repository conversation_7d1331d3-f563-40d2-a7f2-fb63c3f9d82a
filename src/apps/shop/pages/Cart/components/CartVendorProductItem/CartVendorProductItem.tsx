import type { CartItemType } from '@/libs/cart/types';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';
import { ProductActions } from './components/ProductActions/ProductActions';
import { ProductInfo } from './components/ProductInfo/ProductInfo';

export const CartVendorProductItem = ({ data }: { data: CartItemType }) => {
  return (
    <div className="relative grid w-full grid-cols-[160px_1fr_150px] gap-6 pb-4">
      <ProductImage data={data} />
      <ProductInfo data={data} />
      <ProductActions data={data} />
    </div>
  );
};
