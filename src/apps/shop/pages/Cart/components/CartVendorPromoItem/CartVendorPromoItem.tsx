import { Divider, Text } from '@mantine/core';
import { Tooltip } from '@/libs/ui/Tooltip';
import { CartVendorPromotionDisplay } from './CartVendorPromotionDisplay';

import { getPriceString } from '@/utils';
import { BuyXGetYPromotionData } from '@/libs/promotions/types';
import { MODAL_NAME, PROMO_TYPE } from '@/constants';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';

type CartVendorPromoItemProps = {
  promoItem: BuyXGetYPromotionData;
};

export const CartVendorPromoItem = ({
  promoItem,
}: CartVendorPromoItemProps) => {
  const { openModal } = useModalStore();

  if (!promoItem || !promoItem.promotion || !promoItem.freeOffer) return null;

  const handleOpenRemovePromoModal = () => {
    const offerIds = promoItem.items.map((item) => item.productOfferId);
    const totalItems = promoItem.paidItemsQty + promoItem.freeItemsQty;

    openModal({
      name: MODAL_NAME.REMOVE_PRODUCT_FROM_CART,
      offerIds,
      itemName: promoItem.promotion?.name || 'Promotion',
      additionalInfo: `This will remove all ${totalItems} items from this promotion group.`,
    });
  };

  return (
    <div className="relative grid w-full grid-cols-[160px_1fr] gap-6 pb-4">
      <ProductImage
        product={{
          ...promoItem.freeOffer.product,
          productOfferId: promoItem.freeOffer.id,
        }}
      />
      <div className="flex flex-col">
        <div className="mb-2 flex items-center justify-between">
          <div>
            <span className="text-sxs font-medium text-green-700/90">
              Promotion • {PROMO_TYPE[promoItem.promotion.type]}
            </span>
            <h3 className="max-w-9/10 text-sm leading-5 font-semibold text-black">
              {promoItem.promotion.name}
            </h3>
          </div>
          <Tooltip label="Remove promotion from cart">
            <Button
              onClick={handleOpenRemovePromoModal}
              variant="unstyled"
              aria-label="Remove promotion from cart"
            >
              <Icon
                name="trash"
                color="#667085"
                size="1rem"
                aria-hidden={true}
              />
            </Button>
          </Tooltip>
        </div>
        <div className="m-1 mb-3 flex text-center">
          <span className="text-xs text-neutral-500/80">
            SKU:
            <span className="ml-0.5 text-xs font-medium text-[#333333]">
              {promoItem.freeOffer.vendorSku}
            </span>
          </span>
          {promoItem.manufacturer ? (
            <>
              <Divider orientation="vertical" h="1rem" mx="md" />
              <Text ml="2px" c="#344054" size="xs">
                {promoItem.manufacturer}
              </Text>
            </>
          ) : null}
        </div>
        <div className="divider-h"></div>
        {promoItem.items.map((item) => (
          <CartVendorPromotionDisplay key={item.id} item={item} />
        ))}
        <div className="divider-h mb-3"></div>
        <div className="flex w-full items-center justify-between">
          <span className="text-xs text-neutral-600">
            You’re getting total of{' '}
            <strong>{promoItem.paidItemsQty + promoItem.freeItemsQty}</strong>{' '}
            products
          </span>
          <span className="text-xs text-neutral-500">
            Promotional Savings:{' '}
            <span className="text-sm font-medium text-black">
              {getPriceString(
                promoItem.subtotalAllItems - promoItem.subtotalPaidItems,
              )}
            </span>
          </span>
        </div>
      </div>
    </div>
  );
};
