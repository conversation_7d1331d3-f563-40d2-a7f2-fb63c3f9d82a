import { useState } from 'react';
import { Checkbox } from '@/libs/form/Checkbox';
import { useOrderDetails } from '../../services/useOrderDetails';
import { getPriceString } from '@/utils';
import { FEATURE_FLAGS } from '@/constants';
import { OrderVendorPanel } from '../OrderVendorPanel/OrderVendorPanel';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { DownloadInvoicesLink } from '@/libs/orders/components/DownloadInvoicesLink/DownloadInvoicesLink';
import { DownloadChecklist } from '@/libs/orders/components/DownloadChecklist/DownloadChecklist';
import { VendorOrderSummary } from './VendorOrderSummary';
import { getVendorPromotions } from '../../utils/promotion/getVendorPromotions';
import { ORDER_STATUS_CONFIGS } from '../OrderStatus/constants';
import { Button } from '@/libs/ui/Button/Button';

interface OrderDetailsProps {
  id: string;
}
export const OrderDetails = ({ id }: OrderDetailsProps) => {
  const { order, isLoading } = useOrderDetails({ id });
  const [showBackorderedOnly, setShowBackorderedOnly] = useState(false);

  const {
    orderNumber,
    totalPrice,
    downloadInvoicesUrl,
    downloadChecklistUrl,
    vendorOrders = [],
    promotions = [],
  } = order ?? {};

  if (isLoading || !order) {
    return <ContentLoader />;
  }

  const filteredVendorOrders = showBackorderedOnly
    ? vendorOrders
        .filter((vendorOrder) =>
          vendorOrder.items.some(
            (item) =>
              item.status ===
              ORDER_STATUS_CONFIGS.BACKORDERED.label.toLocaleUpperCase(),
          ),
        )
        .map((vendorOrder) => ({
          ...vendorOrder,
          items: vendorOrder.items.filter(
            (item) =>
              item.status ===
              ORDER_STATUS_CONFIGS.BACKORDERED.label.toLocaleUpperCase(),
          ),
        }))
    : vendorOrders;

  return (
    <div className="flex flex-col p-6">
      <div className="flex gap-8">
        <div>
          <div>
            <p className="mb-1 text-xs text-black/60">Order ID</p>
            <span className="text-2xl font-bold">{orderNumber}</span>
          </div>
          <div className="divider-h my-4"></div>
          <div>
            <p className="text-xs text-black/60">Order Total</p>
            <span className="text-2xl font-bold">
              {getPriceString(totalPrice)}
            </span>
          </div>
          <div>
            <div className="divider-h my-4"></div>
            <div className="flex flex-col">
              {downloadInvoicesUrl && (
                <DownloadInvoicesLink url={downloadInvoicesUrl} />
              )}
              {downloadChecklistUrl && (
                <DownloadChecklist url={downloadChecklistUrl} />
              )}
            </div>
          </div>
          {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
            <div className="mb-4 flex flex-col">
              <Button>Repeat Order</Button>
            </div>
          )}
        </div>
        <div className="flex-grow">
          <span className="text-xs text-black/60">Order Details</span>
          <div className={`rounded-sm border border-black/5 bg-[#F8FBFD] p-6`}>
            {vendorOrders.map(
              (
                { items, vendor, totalTaxFee, shippingFee, totalPrice },
                index,
              ) => (
                <VendorOrderSummary
                  key={vendor.id}
                  vendor={vendor}
                  items={items}
                  totalTaxFee={totalTaxFee}
                  shippingFee={shippingFee}
                  totalPrice={totalPrice}
                  showDivider={index !== 0}
                />
              ),
            )}
          </div>
        </div>
      </div>
      <div className="my-5 flex items-center justify-between">
        <div className="divider-h flex-grow"></div>
        <div className="ml-4">
          <Checkbox
            label="Backorder Items Only"
            checked={showBackorderedOnly}
            onChange={(e) => setShowBackorderedOnly(e.target.checked)}
          />
        </div>
      </div>
      {filteredVendorOrders.map(({ vendor, items, totalPrice }) => (
        <div key={vendor.id} className="mb-4">
          <OrderVendorPanel
            totalPrice={+totalPrice}
            items={items}
            vendor={vendor}
            promotions={getVendorPromotions({ id: vendor.id, promotions })}
          />
        </div>
      ))}
    </div>
  );
};
