import { FEATURE_FLAGS } from '@/constants';
import { OrderStatus } from '../../../OrderStatus/OrderStatus';
import { Button } from '@/libs/ui/Button/Button';
import PlusIcon from '@/assets/images/plus.svg?react';
import type { OrderHistoryDetailItemType } from '@/libs/orders/types';
import { OrderHistoryItemContent } from '../OrderHistoryItemContent/OrderHistoryItemContent';
import { Icon } from '@/libs/icons/Icon';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';

export const OrderHistoryRegularItem = ({
  item,
}: {
  item: OrderHistoryDetailItemType;
}) => {
  return (
    <div className="relative flex w-full p-4 px-0">
      <ProductImage
        data={{ product: item.product, productOfferId: item.productOfferId }}
      />
      <div className="flex w-full gap-5">
        <OrderHistoryItemContent item={item} />
        <div className="text-right">
          <p className="mb-1 text-xs font-medium text-gray-500/70">Status</p>
          {item.status && <OrderStatus status={item.status} align="right" />}
          {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
            <Button className="mt-2">
              <Icon name="cartSummary" size={'1.3rem'} />
              <PlusIcon />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
