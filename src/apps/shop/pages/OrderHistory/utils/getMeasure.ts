import { OfferType } from '@/types';

export const getMeasure = ({
  item,
}: {
  item: { productOfferId: string; product: { offers: OfferType[] } };
}) => {
  const unitOfMeasure = item.product.offers.find(
    (offer) => offer.id === item.productOfferId,
  )?.unitOfMeasure;
  const size = item.product.offers.find(
    (offer) => offer.id === item.productOfferId,
  )?.size;

  return `${size ? size + ' ' : ''}${unitOfMeasure}`;
};
