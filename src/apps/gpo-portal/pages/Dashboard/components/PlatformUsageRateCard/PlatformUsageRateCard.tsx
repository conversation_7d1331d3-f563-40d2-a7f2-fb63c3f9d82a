import React, { useEffect } from 'react';
import { DashboardCardWrapper } from '@/libs/dashboard/DashboardCardWrapper/DashboardCardWrapper';
import { DashboardCardLoader } from '@/libs/dashboard/DashboardCardLoader/DashboardCardLoader';
import { DashboardCardError } from '@/libs/dashboard/DashboardCardError/DashboardCardError';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { ProgressBar } from '@/libs/dashboard/ProgressBar/ProgressBar';
import { Select } from '@/libs/form/Select';
import { useTimePeriod, PeriodOption } from '@/libs/utils/hooks/useTimePeriod';
import { usePlatformUsageSummary } from './hooks/usePlatformUsageSummary';
import { exportCsv } from '@/libs/dashboard/utils/exportCsv';

interface PlatformUsageRateCardProps {
  className?: string;
}

export const PlatformUsageRateCard: React.FC<PlatformUsageRateCardProps> = ({
  className,
}) => {
  const { period, setPeriod, options, startDate, endDate } = useTimePeriod({
    defaultPeriod: 'entire year',
    availableOptions: ['entire year', 'Q1', 'Q2', 'Q3', 'Q4'],
  });

  const { data, isLoading, error, updateFilters } = usePlatformUsageSummary({
    date_from: startDate?.format('YYYY-MM-DD'),
    date_to: endDate?.format('YYYY-MM-DD'),
  });

  const timePeriods = options.map((option) => ({
    value: option,
    label:
      option.charAt(0).toUpperCase() +
      option.slice(1).replace(/([A-Z])/g, ' $1'),
  }));

  useEffect(() => {
    if (startDate && endDate) {
      updateFilters({
        date_from: startDate.format('YYYY-MM-DD'),
        date_to: endDate.format('YYYY-MM-DD'),
      });
    }
  }, [startDate, endDate, updateFilters]);

  const handleCtaClick = () => {
    // TODO: Implement download functionality
    console.log('Downloading platform usage report...');
  };

  if (isLoading) {
    return (
      <DashboardCardLoader title="Platform Usage Rate" className={className} />
    );
  }

  if (error) {
    return (
      <DashboardCardError className={className} title="Platform Usage Rate" />
    );
  }

  const {
    gpo_clinics: gpoClinics,
    active_clinics: activeClinics,
    active_clinics_percentage: activeClinicsPercentage,
  } = data || {};
  const inactiveClinics = gpoClinics ? gpoClinics - (activeClinics || 0) : 0;
  const inactivePercentage = 100 - (activeClinicsPercentage || 0);

  return (
    <DashboardCardWrapper
      className={className}
      title="Platform Usage Rate"
      headerActions={
        <>
          <div className="w-[140px]">
            <Select
              value={period}
              onChange={(e) => setPeriod(e.target.value as PeriodOption)}
              options={timePeriods}
              showEmptyOption={false}
            />
          </div>
          <Button
            variant="white"
            className="max-w-[60px]"
            aria-label="Download"
            onClick={() => exportCsv('/gpo/platform-usage/export', {})}
          >
            <Icon name="download" aria-hidden={true} />
          </Button>
        </>
      }
    >
      <div className="mb-6 flex flex-1 items-start justify-between">
        <div>
          <p className="text-[2rem] leading-none font-bold text-[#344054]">
            {activeClinicsPercentage}%
          </p>
          <div className="mt-1 text-sm tracking-wide text-gray-500 uppercase">
            OF THE CLINICS ARE ACTIVE
          </div>
        </div>
        <div className="mx-4 h-16 w-px bg-gray-200"></div>
        <div className="flex flex-col gap-2 text-right">
          <div className="text-sm text-[#666]">
            GPO Clinics:{' '}
            <span className="font-bold text-[#333]">
              {gpoClinics?.toLocaleString()}
            </span>
          </div>
          <div className="text-sm text-[#666]">
            HF Active Clinics:{' '}
            <span className="font-bold text-[#333]">
              {activeClinics?.toLocaleString()}
            </span>
          </div>
        </div>
      </div>

      <div className="mb-6 rounded-[.5rem] bg-[#F2F8FC] p-4">
        <ProgressBar
          showLegend={false}
          values={[
            {
              value: activeClinicsPercentage || 0,
              color: 'bg-blue-600',
              label: 'Active',
            },
            {
              value: inactivePercentage,
              color: 'bg-yellow-400',
              label: 'Inactive',
            },
          ]}
        />
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-blue-600"></div>
            <span className="text-sm">
              HF Active Clinics ({activeClinics?.toLocaleString()}){' '}
              {activeClinicsPercentage}%
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-yellow-400"></div>
            <span className="text-sm">
              Inactive Clinics ({inactiveClinics.toLocaleString()}){' '}
              {inactivePercentage}%
            </span>
          </div>
        </div>
      </div>

      <Button
        variant="white"
        size="md"
        className="min-h-[3rem]"
        onClick={handleCtaClick}
      >
        Download Platform Usage Report
      </Button>
    </DashboardCardWrapper>
  );
};
