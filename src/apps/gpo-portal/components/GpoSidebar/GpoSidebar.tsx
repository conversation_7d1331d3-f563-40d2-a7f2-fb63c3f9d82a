import { Button } from '@/libs/ui/Button/Button';
import { Link } from 'react-router-dom';
import {
  SidebarNav,
  type SidebarNavProps,
} from '@/libs/ui/SidebarNav/SidebarNav';
import { Logo } from '@/libs/ui/Logo/Logo';
import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { HF_CONTACT_EMAIL } from '@/constants';
import { Icon } from '@/libs/icons/Icon';
import { GpoUserSection } from '@/apps/gpo-portal/components/GpoUserSection/GpoUserSection';
import styles from './GpoSidebar.module.css';

const GPO_NAV_LINKS: SidebarNavProps[] = [
  {
    title: 'Main menu',
    links: [
      {
        label: 'Home',
        icon: <Icon name="home" size="1.25rem" />,
        path: GPO_ROUTES_PATH.dashboard,
      },
      {
        label: 'Spent Report',
        icon: <Icon name="bankNote" size="1.5rem" />,
        path: GPO_ROUTES_PATH.spendAnalysis,
      },
      // {
      //   label: 'Platform usage',
      //   icon: <PlatformUsageIcon />,
      //   path: '/platform-usage',
      // },
      // {
      //   label: 'Vendors',
      //   icon: <VendorsIcon />,
      //   path: '/vendors',
      // },
    ],
  },
  {
    title: 'Preferences',
    links: [
      {
        label: 'Settings',
        icon: <Icon name="settings" size="1.5rem" />,
        path: '/settings',
      },
    ],
  },
];

export const GpoSidebar = () => {
  return (
    <div className={styles.sidebarRoot}>
      <div className="mx-auto mt-14 mb-6 max-h-[8rem] max-w-[80%]">
        <Link to={GPO_ROUTES_PATH.dashboard}>
          <Logo />
        </Link>
      </div>

      <div className="flex h-full flex-col pb-4">
        <div>
          {GPO_NAV_LINKS.map(({ title, links }) => (
            <SidebarNav key={title} title={title} links={links} theme="dark" />
          ))}
          <div className="mt-4">
            <Button
              href={`mailto:${HF_CONTACT_EMAIL}`}
              variant="default"
              className={styles.needHelpButton}
            >
              <div className="flex gap-2">
                <span>Need Help</span>
              </div>
            </Button>
          </div>
        </div>
      </div>
      <GpoUserSection />
    </div>
  );
};
